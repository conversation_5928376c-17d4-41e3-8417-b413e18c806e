// ifndef define endif 
#ifndef YA_EMAIL_H
#define YA_EMAIL_H

// #include "email_parser.h"
#include <yaProtoRecord/precord.h>

#ifdef __cplusplus
extern "C" {
#endif // __cplusplus

typedef struct DpiEmail dpi_email_t;
typedef struct DpiEmailAttachment dpi_email_attach_t;

dpi_email_t * dpi_email_create();

int dpi_email_imf(dpi_email_t *email, precord_t * record, const char * data, int len);
int dpi_email_imf_norecord(dpi_email_t *email, const char *data);
int dpi_email_imf_attachment(dpi_email_t *email, precord_t *record, const char *data, int len);

int dpi_email_record_put(dpi_email_t *email, precord_t * record);
char * dpi_email_get_by_name(dpi_email_t *email, const char * name);
void dpi_email_destory(dpi_email_t *);

/*----------------------attachment--------------------------------------------*/
void dpi_email_attach_foreach(dpi_email_t *email, void (*callback)(dpi_email_attach_t *, void *), void *arg);
size_t dpi_email_attach_get_length(dpi_email_attach_t *attach);
size_t dpi_email_attach_get_data(dpi_email_attach_t *attach, char *buf, size_t len);
const char * dpi_email_attach_get_name(dpi_email_attach_t *attach);
/*----------------------attachment--------------------------------------------*/

void test_lib(const char * filename);

int dpi_email_decode_iconv(char* in,char*out,int outlen);
#ifdef __cplusplus
}
#endif // __cplusplus

// include
#endif // _YAEMAIL_H