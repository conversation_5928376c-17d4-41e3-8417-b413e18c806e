//
// VMime library (http://www.vmime.org)
// Copyright (C) 2002 <PERSON> <<EMAIL>>
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License as
// published by the Free Software Foundation; either version 3 of
// the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
// General Public License for more details.
//
// You should have received a copy of the GNU General Public License along
// with this program; if not, write to the Free Software Foundation, Inc.,
// 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
//
// Linking this library statically or dynamically with other modules is making
// a combined work based on this library.  Thus, the terms and conditions of
// the GNU General Public License cover the whole combination.
//

#ifndef VMIME_NET_DEFAULTTIMEOUTHANDLER_HPP_INCLUDED
#define VMIME_NET_DEFAULTTIMEOUTHANDLER_HPP_INCLUDED


#include "vmime/config.hpp"


#if VMIME_HAVE_MESSAGING_FEATURES


#include "vmime/net/timeoutHandler.hpp"

#include <ctime>


namespace vmime {
namespace net {


/** A default timeout handler for messaging services. The default action
  * is to throw a exceptions::operation_timed_out exception when an
  * operation is blocked for more than 30 seconds.
  */
class VMIME_EXPORT defaultTimeoutHandler : public timeoutHandler {

public:

	defaultTimeoutHandler();
	~defaultTimeoutHandler();

	bool isTimeOut();
	void resetTimeOut();
	bool handleTimeOut();

private:

	time_t m_startTime;
};


/** A class that creates default timeout handlers.
  */
class defaultTimeoutHandlerFactory : public timeoutHandlerFactory {

public:

	shared_ptr <timeoutHandler> create();
};


} // net
} // vmime


#endif // VMIME_HAVE_MESSAGING_FEATURES

#endif // VMIME_NET_DEFAULTTIMEOUTHANDLER_HPP_INCLUDED
