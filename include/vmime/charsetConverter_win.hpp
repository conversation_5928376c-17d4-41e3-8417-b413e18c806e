//
// VMime library (http://www.vmime.org)
// Copyright (C) 2002 <PERSON> <<EMAIL>>
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License as
// published by the Free Software Foundation; either version 3 of
// the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
// General Public License for more details.
//
// You should have received a copy of the GNU General Public License along
// with this program; if not, write to the Free Software Foundation, Inc.,
// 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
//
// Linking this library statically or dynamically with other modules is making
// a combined work based on this library.  Thus, the terms and conditions of
// the GNU General Public License cover the whole combination.
//

#ifndef VMIME_CHARSETCONVERTER_WIN_HPP_INCLUDED
#define VMIME_CHARSETCONVERTER_WIN_HPP_INCLUDED


#include "vmime/config.hpp"


#if VMIME_CHARSETCONV_LIB_IS_WIN


#include "vmime/charsetConverter.hpp"


namespace vmime {


/** A generic charset converter which uses Windows MultiByteToWideChar
  * and WideCharToMultiByte API functions.
  *
  * ICU or iconv library should always be preferred over this one, even
  * on Windows platform, as MultiByteToWideChar() and WideCharToMultiByte()
  * functions cannot be used easily with streams (no context). Moreover,
  * error handling is very poor, in particular when an invalid sequence
  * is found...
  *
  * Also, "status" is not supported by this converter for the same reason.
  */
class charsetConverter_win : public charsetConverter {

public:

	/** Construct and initialize a Windows charset converter.
	  *
	  * @param source input charset
	  * @param dest output charset
	  * @param opts conversion options
	  */
	charsetConverter_win(
		const charset& source,
		const charset& dest,
		const charsetConverterOptions& opts = charsetConverterOptions()
	);

	void convert(const string& in, string& out, status* st);
	void convert(utility::inputStream& in, utility::outputStream& out, status* st);

	shared_ptr <utility::charsetFilteredOutputStream> getFilteredOutputStream(
		utility::outputStream& os,
		const charsetConverterOptions& opts
	);

private:

	static int getCodePage(const char* name);

	charset m_source;
	charset m_dest;

	charsetConverterOptions m_options;
};


} // namespace


#endif // VMIME_CHARSETCONV_LIB_IS_WIN

#endif // VMIME_CHARSETCONVERTER_WIN_HPP_INCLUDED
