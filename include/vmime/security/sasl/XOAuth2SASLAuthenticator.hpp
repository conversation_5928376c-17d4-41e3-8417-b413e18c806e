//
// VMime library (http://www.vmime.org)
// Copyright (C) 2002 <PERSON> <<EMAIL>>
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License as
// published by the Free Software Foundation; either version 3 of
// the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
// General Public License for more details.
//
// You should have received a copy of the GNU General Public License along
// with this program; if not, write to the Free Software Foundation, Inc.,
// 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
//
// Linking this library statically or dynamically with other modules is making
// a combined work based on this library.  Thus, the terms and conditions of
// the GNU General Public License cover the whole combination.
//

#ifndef VMIME_SECURITY_SASL_XOAUTH2SASLAUTHENTICATOR_HPP_INCLUDED
#define VMIME_SECURITY_SASL_XOAUTH2SASLAUTHENTICATOR_HPP_INCLUDED


#include "vmime/config.hpp"


#if VMIME_HAVE_MESSAGING_FEATURES && VMIME_HAVE_SASL_SUPPORT


#include "vmime/security/sasl/defaultSASLAuthenticator.hpp"


namespace vmime {
namespace security {
namespace sasl {


/** An authenticator that is capable of providing information
  * for XOAuth2 authentication mechanisms (username and access token).
  * This authenticator force using the XOAUTH2 mechanism.
  */
class VMIME_EXPORT XOAuth2SASLAuthenticator : public defaultSASLAuthenticator {

public:

	enum Mode {
		MODE_SUGGEST,    /**< Try XOAUTH2 before other mechanisms. */
		MODE_EXCLUSIVE   /**< Use XOAUTH2 and nothing else. */
	};


	XOAuth2SASLAuthenticator(const Mode mode);
	~XOAuth2SASLAuthenticator();

	const std::vector <shared_ptr <SASLMechanism> > getAcceptableMechanisms(
		const std::vector <shared_ptr <SASLMechanism> >& available,
		const shared_ptr <SASLMechanism>& suggested
	) const;

private:

	Mode m_mode;
};


} // sasl
} // security
} // vmime


#endif // VMIME_HAVE_MESSAGING_FEATURES && VMIME_HAVE_SASL_SUPPORT

#endif // VMIME_SECURITY_SASL_XOAUTH2SASLAUTHENTICATOR_HPP_INCLUDED
