//
// VMime library (http://www.vmime.org)
// Copyright (C) 2002 <PERSON> <<EMAIL>>
//
// This program is free software; you can redistribute it and/or
// modify it under the terms of the GNU General Public License as
// published by the Free Software Foundation; either version 3 of
// the License, or (at your option) any later version.
//
// This program is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the GNU
// General Public License for more details.
//
// You should have received a copy of the GNU General Public License along
// with this program; if not, write to the Free Software Foundation, Inc.,
// 51 Franklin Street, Fifth Floor, Boston, MA 02110-1301 USA.
//
// Linking this library statically or dynamically with other modules is making
// a combined work based on this library.  Thus, the terms and conditions of
// the GNU General Public License cover the whole combination.
//

#ifndef VMIME_UTILITY_INPUTSTREAMPOINTERADAPTER_HPP_INCLUDED
#define VMIME_UTILITY_INPUTSTREAMPOINTERADAPTER_HPP_INCLUDED


#include "vmime/utility/inputStreamAdapter.hpp"

#include <istream>


namespace vmime {
namespace utility {


/** An adapter class for pointer to C++ standard input stream.
  */
class VMIME_EXPORT inputStreamPointerAdapter : public inputStreamAdapter {

public:

	/** @param is input stream to wrap
	  * @param own if set to 'true', the pointer will be deleted when
	  * this object is destroyed
	  */
	inputStreamPointerAdapter(std::istream* is, const bool own = true);
	~inputStreamPointerAdapter();

private:

	std::istream* m_stream;
	const bool m_own;
};


} // utility
} // vmime


#endif // VMIME_UTILITY_INPUTSTREAMPOINTERADAPTER_HPP_INCLUDED
