#include "yaemail/email.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <dirent.h>
#include <sys/stat.h>
// 遍历目录获取所有文件名


    char *fields[] = {"senderEmail",
    "senderAli",
    "FromIp",
    "FromIpCnt",
    "FromDom",
    "FromDomCnt",
    "FromAsn",
    "FromCountry",
    "rcvrEmail",
    "rcvrAli",
    "ByIP",
    "rcvDate",
    "CC",
    "CCAli",
    "BCC",
    "repTo",
    "date",
    "emaProtType",
    "loginSrv",
    "SMTPSrv",
    "SMTPSrvAge",
    "subj",
    "xMai",
    "conTraEnc",
    "conTexCha",
    "bodyType",
    "bodyTypeCnt",
    "conType",
    "conTypeCnt",
    "emaInd",
    "attFileName",
    "attFileNameCnt",
    "attType",
    "attTypeCnt",
    "attMD5",
    "attMD5Cnt",
    "attConSize",
    "headSet",
    "headSetCnt",
    "msgID",
    "msgIDCnt",
    "mimeVer",
    "mimeVerCnt",
    "login",
    "pwd",
    "realFrom",
    "realTo",
    "contentWithHtml",
    "charset",
    "contentLen",
    "host",
    "deliveredTo",
    "xOriIP",
    "startTLS",
    "Command",
    "count",
    "mailFrom",
    "mailFromDom",
    "mailFromDomCnt",
    "rcvrDom",
    "rcptTo",
    "rcptToDom",
    "rcptToDomCnt",
    "resentFrom",
    "resentTo",
    "resentDate",
    "bodyLen",
    "bodyTraEnc",
    "bodyTexCha",
    "name",
    "vendor",
    "ver",
    "os",
    "osVer",
    "rcvrEmailCnt",
    "subjectCnt",
    "xMailerCnt",
    "received",
    "with",
    "ByDom",
    "ByAsn",
    "ByCountry",
    "usrAge",
    "senderDom",
    "content",
    "resentSrvAge",
    "body",
    "bodyURL",
    "spf",
    "envelope_from",
    "envelope_from_domain",
    "envelope_from_domain_count",
    "from_ip_count",
    "from_domain_count",
    "by_ip_count",
    "by_domain_count",
    "resent_agent",
    "cc_alias",
    "body_md5",
    "body_length",
    "body_url_count",
    "index",
    "attachment_md5_count",
    "header_set_count",
    "subject_count",
    "x_mailer_count",
    "receiver_alias",
    "sender_alias",
    "reply",
    "sender_domain",
    "sender_software",
    "user_agent"};

void getFiles(char * path, char * files[])
{
  DIR * dir;
  struct dirent * ptr;
  int i = 0;
  
  dir = opendir(path);
  while((ptr = readdir(dir)) != NULL)
  {
    if(strcmp(ptr->d_name, ".") == 0 || strcmp(ptr->d_name, "..") == 0)
      continue;
    else if(ptr->d_type == 8) // file
    {
      files[i] = malloc(strlen(ptr->d_name) + 1);
      strcpy(files[i], ptr->d_name);
      i++;
    }
    else if(ptr->d_type == 10) // link file
    {
      files[i] = malloc(strlen(ptr->d_name) + 1);
      strcpy(files[i], ptr->d_name);
      i++;
    }
    else if(ptr->d_type == 4) // dir
    {
      char * sub_path = malloc(strlen(path) + strlen(ptr->d_name) + 2);
      strcpy(sub_path, path);
      strcat(sub_path, "/");
      strcat(sub_path, ptr->d_name);
      getFiles(sub_path, files);
    }
  }
  closedir(dir);
}


void attachment_callback(dpi_email_attach_t *attach, void *data)
{
  char *buf = NULL;
  size_t len = 0;
  len = dpi_email_attach_get_length(attach);
  buf = malloc(len + 1);
  int ret = dpi_email_attach_get_data(attach, buf, len);
  if (ret <= 0) {
    return;
  }
  const char *filename = dpi_email_attach_get_name(attach);

  printf("----------------------------------------------------\n");
  printf("filename = %s\n", filename);
  printf("data = %s\n", buf);
  printf("----------------------------------------------------\n");

  free(buf);
  buf = NULL;
  return;
  
}

int main()
{
  int i = 0, r = 0;
  char *files[20] = {0};
  char filename[256] = {0 };
  struct stat stat_info;
  dpi_email_t *email;
  char *data;
  FILE *f;
  getFiles("../../emails", files);
  while (files[i] != NULL)
  {
    printf("%s\n", files[i]);
    snprintf(filename, 256, "../../emails/%s", files[i]);
    f = fopen(filename, "r");
    if (f == NULL) {
      continue;
    }

    r = stat(filename, &stat_info);

    data = malloc(stat_info.st_size);
    fread(data, 1, stat_info.st_size, f);
    fclose(f);

    email = dpi_email_create();
    dpi_email_imf_attachment(email, NULL, data, stat_info.st_size);
    dpi_email_imf(email, NULL, (const char *)data, stat_info.st_size);
    printf("header--------\n");
    for(int i = 0;i< sizeof(fields)/sizeof(fields[0]);i++)
    {
      char *value = dpi_email_get_by_name(email, fields[i]);
      if (value != NULL) {
        printf("%s = %s\n", fields[i], value);
      }
    }
    printf("header end--------\n");

    // 获取正文
    // char * body = dpi_email_get_by_name(email, "content");

    // printf("--------------------------content-----------------------\n");
    // printf("%s\n", body);
    // printf("--------------------------content-----------------------\n");

    // dpi_email_attach_foreach(email, attachment_callback, NULL);

    dpi_email_destory(email);

    free(data);
    i++;
  }

  return 0;
}