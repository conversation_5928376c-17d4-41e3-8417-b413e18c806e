#include "email_text.h"
#include "yaemail/email.h"

#include <iconv.h>
#include<iostream>

std::string base64_decode(const std::string & str)
{
  static const std::string base64_chars =
    "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
    "abcdefghijklmnopqrstuvwxyz"
    "0123456789+/";
  std::string ret;
  int val = 0;
  int valb = -8;
  for (std::size_t i = 0; i < str.size(); ++i) {
    unsigned char c = str[i];
    if (std::isspace(c)) {
      continue;
    }
    if(c == '\n')
        continue;
    if(c == '\r' && i + 1 != str.size() && str[i+1] == '\n') {
        i++;
        continue;
    }
    auto pos = base64_chars.find(c);
    if (pos == std::string::npos) {
      break;
    }
    val = (val << 6) + pos;
    valb += 6;
    if (valb >= 0) {
      ret.push_back(char((val >> valb) & 0xFF));
      valb -= 8;
    }
  }
  return ret;
}

std::string & email_decode(std::string & str)
{
  auto  pos = str.find("=?", 0);
  while (pos!= std::string::npos) {
    std::string::size_type pos2 = str.find("?=", pos);
    if (pos2 == std::string::npos) {
      break;
    }
    std::string::size_type pos3 = str.find("?", pos + 2);
    if (pos3 == std::string::npos || pos3 >= pos2) {
      break;
    }
    std::string::size_type pos4 = str.find("?", pos3 + 1);
    if (pos4 == std::string::npos || pos4 >= pos2) {
      break;
    }
    std::string charset = str.substr(pos + 2, pos3 - pos - 2);
    std::string encoding = str.substr(pos3 + 1, pos4 - pos3 - 1);
    std::string text = str.substr(pos4 + 1, pos2 - pos4 - 1);

    if (encoding == "B" || encoding == "b") {
      text = base64_decode(text);
      if (charset != "UTF-8" && charset != "utf-8") {
        iconv_t cd = iconv_open("UTF-8", charset.c_str());
        if (cd == (iconv_t)-1) {
#ifdef LIBEMAIL_DEBUG
        std::cout << "iconv_open error" << std::endl;
#endif
        } else {
          char *inbuf = (char *)text.c_str();
          size_t inlen = text.size();
          char outbuf[1024];
          char *out = outbuf;
          size_t outlen = sizeof(outbuf);
          size_t ret = iconv(cd, &inbuf, &inlen, &out, &outlen);
          if (ret == (size_t)-1) {
#ifdef LIBEMAIL_DEBUG
            std::cout << "iconv error" << std::endl;
#endif
          } else {
            text = std::string(outbuf, sizeof(outbuf) - outlen);
          }
          iconv_close(cd);
        }
      }
    } else if (encoding == "Q" || encoding == "q") {
    }

    str.replace(pos, pos2 - pos + 2, text);
    pos += text.size();
    pos = str.find("=?", pos);  
  }
  return str;
}

std::string DpiEmailText::GetBuff(std::string &in, vmime::charset ch) {
    if (in.find("GB2312") != std::string::npos) {
        return email_decode(in);
    }
    
    vmime::text txt;
    txt.parse(in, 0, in.length());

    std::string out = txt.getWholeBuffer();


    return out;
}
int dpi_email_decode_iconv(char* in,char*out,int outlen){
    std::string str = in;
    std::string out_str = email_decode(str);
    int copylen = outlen>out_str.size()?out_str.size():outlen-1;
      if(copylen>0)
    {
    memcpy(out,out_str.c_str(),copylen);
    }

    return out_str.size();
}
