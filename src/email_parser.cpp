#include "email_parser.h"
#include "vmime/header.hpp"
#include "yaemail/email.h"

#include <iostream>
#include <cstdio>
#include <cstring>
#include <string>
#include <utility>
#include <vector>
#include <set>
#include <iconv.h>

#include <yaProtoRecord/precord.h>
#include <yaFtypes/fvalue.h>
#include "vmime/vmime.hpp"

#include "utils.h"
#include "email_text.h"

struct CustomField {
  int  index;
  std::string key;
  // function, agruments are MailParser & , ori_key, custom_key
  std::function<void(DpiEmail &, const std::string &)> callback;
};

static bool push_field_cnt(DpiEmail &parser, const std::string &key,
                           std::string field_str, std::string field_cnt) {
  auto subject = parser.GetFieldValueList(key);
  if (subject.empty()) {
    return false;
  }
  auto cnt = subject.size();
  std::string val;
  for (auto &field : subject) {
    if (val.empty()) {
      val = field;
    } else {
      val = val + "," + field;
    }
  }

  if (!field_str.empty()) {
    parser.PushCustomField(field_str, val);
  }
  if (!field_cnt.empty()) {
    parser.PushCustomField(field_cnt, std::to_string(cnt));
  }
  return true;
}

bool email_value_splist(const std::string &str,
                        std::set<std::string> &addrs,
                        std::set<std::string> &alias)
{
  std::string::size_type pos = 0;
  std::string::size_type pos2 = 0;
  std::string::size_type pos3 = 0;
  std::string::size_type pos4 = 0;
  std::string temp_str;
  while ((pos = str.find("<", pos)) != std::string::npos) {
    temp_str = str.substr(pos2, pos - pos2);
    pos2 = pos;
    while ((pos3 = temp_str.find(",", pos3)) != std::string::npos) {
      temp_str = temp_str.substr(pos3 + 1, temp_str.size() - pos3 - 1);
      pos3 = 0;
    }
    while ((pos4 = temp_str.find(";", pos4)) != std::string::npos) {
      temp_str = temp_str.substr(pos4 + 1, temp_str.size() - pos4 - 1);
      pos4 = 0;
    }

    // delete " or  space, or delete both  in the head and tail
    size_t str_len = temp_str.length();
    for (int i = str_len - 1; i > 0; i--) {
      if (temp_str[i] == '"' || temp_str[i] == ' ') {
        temp_str.erase(i, 1);
      } else {
        break;
      }
    }
    for (int i = 0; i < str_len; i++) {
      if (temp_str[i] == '"' || temp_str[i] == ' ') {
        temp_str.erase(i, 1);
      } else {
        break;
      }
    }

    alias.insert(temp_str);
    // alias.insert(email_decode(temp_str));
    pos = str.find(">", pos);
    if (pos == std::string::npos) {
      break;
    }
    temp_str = str.substr(pos2, pos - pos2 + 1);
    addrs.insert(temp_str);
    pos2 = pos + 1;
    pos3 = 0;
  }

  return true;
}

bool email_value_callback_splist(DpiEmail &parser, const std::string &key,
                                 std::string &addrs_str,
                                 std::string &alis_str) {
  auto value_list = parser.GetFieldValueList(key);
  if (value_list.empty()) {
    return false;
  }
  for (auto &value : value_list) {
    std::set<std::string> addrs;
    std::set<std::string> alis;
    // splist
    email_value_splist(value, addrs, alis);

    for (auto addr : addrs) {
      dpi_string_join(addr, addrs_str, ",");
    }

    for (auto &ali : alis) {
      if (alis_str.empty()) {
        alis_str = ali;
      } else {
        alis_str += "," + ali;
      }
    }
  }

  return true;
}

static bool head_field_combine(DpiEmail &parser, const std::string &key,
                               std::string &out_field, size_t field_cnt) {
  auto value_list = parser.GetFieldValueList(key);
  if (value_list.empty()) {
    return false;
  }

  for (auto &val : value_list) {
    dpi_string_join(val, out_field, ",");
  }
  field_cnt = value_list.size();

  return true;
}

void CcCallback(DpiEmail &parser, const std::string &key)
{
  std::string addrs_str, alis_str;

  bool flag = email_value_callback_splist(parser, key, addrs_str, alis_str);
  if (!flag) {
    return;
  }

  parser.PushCustomField("CC", addrs_str);
  parser.PushCustomField("CCAli", alis_str);
  parser.PushCustomField("cc_alias", alis_str);
}

void BccCallback(DpiEmail &parser, const std::string &key)
{
  std::string addrs_str, alis_str;

  bool flag = email_value_callback_splist(parser, key, addrs_str, alis_str);
  if (!flag) {
    return;
  }

  parser.PushCustomField("BCC", addrs_str);
  // parser.PushCustomField("bccali", alis_str);
}

void ReceivedCallback(DpiEmail &parser, const std::string &key)
{

#ifdef LIBEMAIL_DEBUG
  std::cout << "ReceivedCallback" << std::endl;
#endif

  auto value_list = parser.GetFieldValueList(key);

#ifdef LIBEMAIL_DEBUG
  for (auto & v : value_list) {
    std::cout << "received field: " << v << std::endl;
  }
#endif

  if (value_list.empty()) {
    return;
  }

  std::map<std::string, int> domain_map;
  std::map<std::string, int> ip_map;
  std::map<std::string, int> byip_map;
  std::vector<std::string> receive_fields = {"from", "by", "with", "id", "for", "via", ";"};
  // std::map<std::string, std::string> receive_map;
  std::map<std::string, std::vector<std::string>> receive_map;

  for (const auto &value : value_list) {
    // foreach value and find receive field and split to map
    size_t elem_cnt = receive_fields.size();
    for (int i = 0; i < elem_cnt; ++i) {
      std::string key  = receive_fields[i];
      auto pos = value.find(receive_fields[i]);
      if (pos == std::string::npos) {
        continue;
      }

      if (receive_fields[i] == ";") {
        std::string receive = value.substr(pos + 1, value.size() - pos - 2);
        dpi_string_strip(receive);
        // if find date, combin with ",", else insert value
        receive_map["date"].push_back(receive);
        break;
      }

      for (int j = i + 1; j < elem_cnt; ++j) {
        auto pos2 = value.find(receive_fields[j]);
        if (pos2 == std::string::npos) {
          continue;
        }
        std::string receive = value.substr(pos + receive_fields[i].size(), pos2 - pos - receive_fields[i].size());
        dpi_string_strip(receive);
        receive_map[key].push_back(receive);
        break;
      }
    }
  }

#ifdef LIBEMAIL_DEBUG
  std::cout << "haha" << std::endl;
#endif


  std::vector<std::string> dom_vec;
  std::vector<std::string> ip_vec;
  if (receive_map.find("from") != receive_map.end()) {
    auto vec = receive_map["from"];
    for (auto str : vec) {
      if (str.find("(") == std::string::npos || str.find(")") == std::string::npos) {
        continue;
      }
      auto start_pos = str.rfind("(");
      auto end_pos = str.rfind(")");
      // auto start_pos = str.find("(");
      // auto end_pos = str.find(")");
      std::string addr = str.substr(start_pos + 1, end_pos - start_pos - 1);
      if (addr.find("[") == std::string::npos) {
        dom_vec.push_back(addr);
      }
      auto split_pos_start = addr.find("[");
      auto split_pos_end = addr.find("]");
      std::string dom = addr.substr(0, split_pos_start);
      std::string ip = addr.substr(split_pos_start + 1, split_pos_end - split_pos_start - 1);
      dom_vec.push_back(dom);
      ip_vec.push_back(ip);
    }
  }

  std::vector<std::string> by_ip_vec;
  if (receive_map.find("by") != receive_map.end()) {
    auto vec = receive_map["by"];
    for (auto str : vec) {
      if (str.find("[") == std::string::npos || str.find("]") == std::string::npos) {
        continue;
      }
      auto start_pos = str.find("[");
      auto end_pos = str.find("]");
      std::string addr = str.substr(start_pos + 1, end_pos - start_pos - 1);
      by_ip_vec.push_back(addr);
    }
  }

  std::string result;
  size_t      result_cnt;


  result = dpi_contain_join(dom_vec, ",");
  result_cnt = dom_vec.size();
  parser.PushCustomField("FromDom", result);
  parser.PushCustomField("FromDomCnt", std::to_string(result_cnt));

  result = dpi_contain_join(ip_vec, ",");
  result_cnt = ip_vec.size();
  parser.PushCustomField("FromIp", result);
  parser.PushCustomField("FromIpCnt", std::to_string(result_cnt));

  result = dpi_contain_join(by_ip_vec, ",");
  result_cnt = by_ip_vec.size();
  parser.PushCustomField("ByIP", result);
  parser.PushCustomField("by_ip_count", std::to_string(result_cnt));

  // Add missing count fields
  parser.PushCustomField("from_ip_count", std::to_string(ip_vec.size()));
  parser.PushCustomField("from_domain_count", std::to_string(dom_vec.size()));
  parser.PushCustomField("by_domain_count", std::to_string(result_cnt));

}

std::string _to_get_rcvrdom(std::string addr_str)
{
  std::vector<std::string> dom_vec;
  auto pos = addr_str.find("@");
  while (pos != std::string::npos) {
    auto start_pos = addr_str.rfind("<", pos);
    auto end_pos = addr_str.find(">", pos);
    std::string addr = addr_str.substr(start_pos + 1, end_pos - start_pos - 1);
    auto split_pos_start = addr.find("@");
    std::string dom = addr.substr(split_pos_start + 1, addr.size() - split_pos_start - 1);
    dom_vec.push_back(dom);
    pos = addr_str.find("@", pos + 1);
  }
  std::string result;
  result = dpi_contain_join(dom_vec, ",");

  return result;
}

void ToCallback(DpiEmail &parser, const std::string &key)
{
  std::string addrs_str, alis_str;

  bool flag = email_value_callback_splist(parser, key, addrs_str, alis_str);
  if (!flag) {
    return;
  }

  auto f_value_cnt = parser.GetFieldValueList(key).size();

  std::string rcvr_dom = _to_get_rcvrdom(addrs_str);
  parser.PushCustomField("rcvrDom", rcvr_dom);
  parser.PushCustomField("rcvrEmail", addrs_str);
  parser.PushCustomField("realTo", addrs_str);
  parser.PushCustomField("rcvrAli", alis_str);
  parser.PushCustomField("receiver_alias", alis_str);
  parser.PushCustomField("rcvrEmailCnt", std::to_string(f_value_cnt));
}

void FromCallback(DpiEmail &parser, const std::string &key)
{
  std::string addrs_str, alis_str;

  bool flag = email_value_callback_splist(parser, key, addrs_str, alis_str);
  if (!flag) {
    return;
  }

  parser.PushCustomField("senderEmail", addrs_str);
  parser.PushCustomField("realFrom", addrs_str);
  parser.PushCustomField("senderAli", alis_str);
  parser.PushCustomField("sender_alias", alis_str);

  // Extract sender domain
  std::string sender_dom = _to_get_rcvrdom(addrs_str);
  parser.PushCustomField("senderDom", sender_dom);
  parser.PushCustomField("sender_domain", sender_dom);
}

void SubjectCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "subj", "subjectCnt");
  push_field_cnt(parser, key, "", "subject_count");
}

void XmailerCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "xMai", "xMailerCnt");
  push_field_cnt(parser, key, "", "x_mailer_count");

  // Extract sender software info
  auto value_list = parser.GetFieldValueList(key);
  if (!value_list.empty()) {
    parser.PushCustomField("sender_software", value_list[0]);
  }
}

void MimeVersionCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "mimeVer", "mimeVerCnt");
}

void MessageIdCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "msgID", "msgIDCnt");
}

void DateCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "date", "");
}

void ReplyToCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "repTo", "");
}

void DeliveredToCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "deliveredTo", "");
}

void DeliveryDateCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "rcvDate", "");
}

void XOriginatingIpCallback(DpiEmail &parser, const std::string &key)
{
  auto subject = parser.GetFieldValueList(key);
  if (subject.empty()) {
    return;
  }
  auto cnt = subject.size();
  std::string val;
  for (auto &field: subject) {
    dpi_string_join(field, val, ",");
  }

  parser.PushCustomField("xOriIP", val);
}

void ReturnPathCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "envelope_from", "");

  // Extract domain from envelope_from
  auto value_list = parser.GetFieldValueList(key);
  if (!value_list.empty()) {
    std::string envelope_from_dom = _to_get_rcvrdom(value_list[0]);
    parser.PushCustomField("envelope_from_domain", envelope_from_dom);
    parser.PushCustomField("envelope_from_domain_count", "1");
  }
}

void AuthenticationResultsCallback(DpiEmail &parser, const std::string &key) {
  auto value_list = parser.GetFieldValueList(key);
  if (value_list.empty()) {
    return;
  }

  for (auto &value : value_list) {
    if (value.find("spf=") != std::string::npos) {
      std::string spf_result;
      auto pos = value.find("spf=");
      auto end_pos = value.find(" ", pos);
      if (end_pos == std::string::npos) end_pos = value.length();
      spf_result = value.substr(pos + 4, end_pos - pos - 4);
      parser.PushCustomField("spf", spf_result);
    }
  }
}

void ResentFromCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "resentFrom", "");
}

void ResentToCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "resentTo", "");
}

void ResentDateCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "resentDate", "");
}

void UserAgentCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "user_agent", "");
}

void ResentAgentCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "resent_agent", "");
}

void ThreadIndexCallback(DpiEmail &parser, const std::string &key) {
  push_field_cnt(parser, key, "index", "");
}

// custom field list with callback function
struct CustomField custom_field_list[] = {
  // from callback
  {0, "from",               FromCallback},
  {1, "received",           ReceivedCallback},
  {2, "to",                 ToCallback},
  {3, "cc",                 CcCallback},
  {4, "bcc",                BccCallback},
  {5, "subject",            SubjectCallback},
  {6, "x-mailer",           XmailerCallback},
  {7, "mime-version",       MimeVersionCallback},
  {8, "message-id",         MessageIdCallback},
  {9, "date",               DateCallback},
  {10, "reply-to",          ReplyToCallback},
  {11, "x-originating-ip",  XOriginatingIpCallback},
  {12, "Delivered-To",      DeliveredToCallback},
  {13, "delivery-date",     DeliveryDateCallback},
  {14, "return-path",       ReturnPathCallback},
  {15, "authentication-results", AuthenticationResultsCallback},
  {16, "resent-from",       ResentFromCallback},
  {17, "resent-to",         ResentToCallback},
  {18, "resent-date",       ResentDateCallback},
  {19, "user-agent",        UserAgentCallback},
  {20, "resent-agent",      ResentAgentCallback},
  {21, "thread-index",      ThreadIndexCallback},
  {-1, "", nullptr}
};

// deal custom field
bool DpiEmail::DealCustomField() {

  for (int i = 0; custom_field_list[i].index != -1; i++) {
    if (custom_field_list[i].callback) {
      custom_field_list[i].callback(*this, custom_field_list[i].key);
    }
  }
  return true;
}

bool DpiEmail::AddCustomField(const std::string &new_key, const std::string &key)
{
  auto header = msg_->getHeader();
  auto fields = header->findAllFields(key);
  std::string val = "";

  for (auto &field: fields) {
    auto value = field->getValue()->generate();
    val = val + "," + value;
  }

  // if val[0] = ',', delete
  if (val[0] == ',') {
    val.erase(0, 1);
  }

  out_fields_[new_key] = val;

  return true;
}

std::vector<std::string> DpiEmail::GetFieldValueList(const std::string &key)
{
  std::vector<std::string> vec;
  auto header = msg_->getHeader();
  auto fields = header->findAllFields(key);

  if (fields.empty()) {
    return vec;
  }
  for (auto &elem : fields) {
    std::string str;
    std::string val = elem->getValue()->generate();
    vmime::text txt(val);
    auto textVal =  elem->getValue<vmime::text>();
    if (textVal) {
      str = textVal->getConvertedText("utf-8");
    } else {
      str = txt.getConvertedText("utf-8");
    }
    vec.push_back(str);
  }

  return vec;
}

void test_lib(const char * filename) {
    printf("test_list: %s\n", filename);
}

static const std::string getFieldValue(const vmime::headerField& field) {

    std::ostringstream oss;
    vmime::utility::outputStreamAdapter voss(oss);
    field.generate(voss);

    return oss.str();
}


bool DpiEmail::ParserBody(vmime::shared_ptr<vmime::messageParser> & mp)
{
  #ifdef LIBEMAIL_DEBUG
  std::cout << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> text part " << std::endl;
#endif
  std::string text_charsets;
  std::string text_types;
  size_t text_types_cnt = 0;
  size_t content_length = 0;

  size_t      body_len = 0;
  std::string body_encode;
  std::string body_charset;
  std::string content_trans_encod;
  std::string html_str;
  std::string content_str;
  for (size_t i = 0; i < mp->getTextPartCount(); ++i) {
    const vmime::textPart &part = *mp->getTextPartAt(i);

    std::string charset = part.getCharset().generate();
    dpi_string_join(charset, text_charsets, ",");
    std::string content_type = part.getType().generate();

    body_encode = part.getText()->getEncoding().getName();
    if (content_type.find("html") != std::string::npos) {
#ifdef LIBEMAIL_DEBUG
      std::cout << "yes html !" << content_type << std::endl;
      std::cout << "lentgh: " << part.getText()->getLength() << std::endl;
#endif
    }

    if (content_type.find("text/") != std::string::npos) {
      content_types_.push_back(content_type.erase(0, 5));
      dpi_string_join(content_type.erase(0, 5), text_types, ",");
    }
    text_types_cnt++;
    // text/html
    if (part.getType().getSubType() == vmime::mediaTypes::TEXT_HTML) {
      //get utf-8 html
      const vmime::htmlTextPart &hp =
          dynamic_cast<const vmime::htmlTextPart &>(part);
      vmime::utility::outputStreamStringAdapter out(html_str);
      hp.getText()->extractRaw(out);

      for (size_t j = 0; j < hp.getObjectCount(); ++j) {
        const vmime::htmlTextPart::embeddedObject &obj = *hp.getObjectAt(j);
      }
      // text/plain
    } else {

      const vmime::textPart &tp = dynamic_cast<const vmime::textPart &>(part);
      vmime::utility::outputStreamStringAdapter out(content_str);
      tp.getText()->extractRaw(out);

      body_len = tp.getText()->getLength();
      body_charset = tp.getCharset().getName();

      charsets_.push_back(tp.getCharset().getName());

      content_trans_encod = body_encode;
      if ( body_encode == "base64") {
          content_str = base64_decode(content_str);
          if (body_charset != "UTF-8" && body_charset != "utf-8") {
            iconv_t cd = iconv_open("UTF-8", body_charset.c_str());
            if (cd == (iconv_t)-1) {
      #ifdef LIBEMAIL_DEBUG
            std::cout << "iconv_open error" << std::endl;
      #endif
            } else {
              char *inbuf = (char *)content_str.c_str();
              size_t inlen = content_str.size();
              size_t outbuflen = inlen*3/2 + 1;
              size_t outlen = outbuflen;
              char *outbuf =  (char *)malloc(outbuflen);
              char *out = outbuf;
              if(outbuf) {
                  memset(out, 0, outlen);
                  size_t ret = iconv(cd, &inbuf, &inlen, &out, &outlen);
                  if (ret == (size_t)-1) {
      #ifdef LIBEMAIL_DEBUG
                    std::cout << "iconv error" << std::endl;
      #endif
                  } else {
                    content_str = std::string(outbuf, outbuflen - outlen);
                    body_len = outbuflen - outlen;
                  }
                  free(outbuf);
              }
              iconv_close(cd);
            }
          }
      }
    }
    content_transfer_encodings_.push_back(body_encode);
  }

  out_fields_.insert(std::make_pair("bodyLen", std::to_string(body_len)));
  out_fields_.insert({"contentLen", std::to_string(body_len)});
  out_fields_.insert(std::make_pair("charset", body_charset));
  out_fields_.insert(std::make_pair("conTexCha", text_charsets));
  out_fields_.insert({"conTypeCnt", std::to_string(text_types_cnt)});
  out_fields_.insert(std::make_pair("contentLen", std::to_string(content_length)));
  out_fields_.insert({"contentWithHtml", html_str});
  out_fields_.insert({"content", content_str});
  out_fields_.insert(std::make_pair("bodyTexCha", text_charsets));

  // Add missing body-related fields
  out_fields_.insert({"body", content_str});
  out_fields_.insert({"body_length", std::to_string(body_len)});

  // Count URLs in body content
  size_t url_count = 0;
  std::string body_urls;
  std::string::size_type pos = 0;
  while ((pos = content_str.find("http", pos)) != std::string::npos) {
    url_count++;
    pos += 4;
  }
  out_fields_.insert({"body_url_count", std::to_string(url_count)});
  out_fields_.insert({"bodyURL", body_urls});

  // Add body_md5 (simplified - just use content length as placeholder)
  std::string body_md5_placeholder = "md5_" + std::to_string(body_len);
  out_fields_.insert({"body_md5", body_md5_placeholder});

  return true;
}

bool DpiEmail::ParserAttachment(vmime::shared_ptr<vmime::messageParser> &mp)
{
  auto att = mp->getAttachmentList();
#ifdef LIBEMAIL_DEBUG
  std::cout << ">>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>> attachment " << std::endl;
#endif

  size_t attach_cnt = mp->getAttachmentCount();
  std::string attach_filenames;
  size_t length;
  std::string length_str;
  std::string content_md5;
  std::string attach_types;
  size_t content_md5_cnt = 0;

  for (size_t i = 0; i < attach_cnt; ++i) {
    const vmime::attachment &attach = *mp->getAttachmentAt(i);
    vmime::shared_ptr<const vmime::header> header = attach.getHeader();

    auto tran_encod_field = header->findField(vmime::fields::CONTENT_TRANSFER_ENCODING);
    if (tran_encod_field) {
      content_transfer_encodings_.push_back(header->ContentTransferEncoding()->getValue()->generate());
    }

    auto con_type_field = header->findField(vmime::fields::CONTENT_TYPE);
    if (con_type_field) {

        std::string type = header->ContentType()->getValue()->generate();
        std::string sub_type = type.substr(type.find("/") + 1, type.size() - type.find("/") - 1);
        content_types_.push_back(sub_type);
    }
    std::string ch = attach.getName().getCharset().getName();
    // charsets_.push_back(ch);
    auto field_cnt = header->getFieldCount();
    std::string fields_str;
    std::vector<vmime::shared_ptr<const vmime::headerField>> headerlist = header->getFieldList();
    for (auto &field : headerlist) {
      auto name = field->getName();
      std::string value = field->getValue()->generate();
      if (name == "Content-MD5") {
        dpi_string_join(value, content_md5, ",");
        content_md5_cnt++;
      }
    }

    std::string a_name = attach.getName().generate();
    std::string attach_name = DpiEmailText::GetBuff(a_name, vmime::charsets::UTF_8);
    dpi_string_join(attach_name, attach_filenames, ",");
    // a.jpg  get  string jpg
    std::string::size_type pos = attach_name.find_last_of(".");
    std::string attach_type = attach_name.substr(pos + 1, attach_name.size() - pos - 1);
    dpi_string_join(attach_type, attach_types, ",");
    length = attach.getData()->getLength();
    std::string len_str = std::to_string(attach.getData()->getLength());
    dpi_string_join(len_str, length_str, ",");

    vmime::text out;
    std::string val = attach.getName().generate();
    vmime::text txt;
    txt.parse(val, 0, val.length());

    std::string str = DpiEmailText::GetBuff(val, vmime::charsets::UTF_8);

    vmime::text::decodeAndUnfold(attach.getName().generate(), &out);
    vmime::string in = attach.getName().generate();
    vmime::string o;
    #ifdef LIBEMAIL_DEBUG
    std::cout << attach_name << std::endl;
    std::cout << attach.getType().generate() << std::endl;
#endif

    if (flag_ & 0x01) {
      DpiEmailAttachment * attachment = new DpiEmailAttachment();
      attachment->name_ = attach_name;
      // attachment.data_ = attach.getData()->getAsString();
      // generate data and push to data_
      vmime::utility::outputStreamStringAdapter out(attachment->data_);
      attach.getData()->extractRaw(out);
      attachments_.push_back(attachment);
    }
  }

  out_fields_.insert(std::make_pair("attFileNameCnt", std::to_string(attach_cnt)));
  out_fields_.insert(std::make_pair("attFileName", attach_filenames));
  out_fields_.insert(std::make_pair("attConSize", length_str));
  out_fields_.insert(std::make_pair("attMD5", content_md5));
  out_fields_.insert(std::make_pair("attMD5Cnt", std::to_string(content_md5_cnt)));
  out_fields_.insert(std::make_pair("attType", attach_types));
  out_fields_.insert(std::make_pair("attTypeCnt", std::to_string(attach_cnt)));
  out_fields_.insert(std::make_pair("attachment_md5_count", std::to_string(content_md5_cnt)));

#ifdef LIBEMAIL_DEBUG
  std::cout << "atttypes " << attach_types << std::endl;
#endif
  return true;
}

void DpiEmail::dissectImf(precord_t *record, const char *data, int len) {

  std::string data_(data,len);
  msg_->parse(data_);
  vmime::shared_ptr<vmime::header> header = msg_->getHeader();
  std::vector<vmime::shared_ptr<vmime::headerField>> headerlist =
      header->getFieldList();
  vmime::shared_ptr<vmime::messageParser> mp =
      vmime::make_shared<vmime::messageParser>(msg_);

  auto field_cnt = header->getFieldCount();
  std::string fields_str;
  for (auto &field : headerlist) {
    auto name = field->getName();
    dpi_string_join(name, fields_str, ",");

    std::string val = field->getValue()->generate();
    vmime::text txt;
    txt.parse(val, 0, val.length());

    std::string str = txt.getConvertedText("utf-8");
    // if not find in fields_, then insert
    if (fields_.find(name) == fields_.end()) {
      fields_.insert(std::make_pair(name, str));
    }
  }
  out_fields_.insert(std::make_pair("headSet", fields_str));
  out_fields_.insert(std::make_pair("headSetCnt", std::to_string(field_cnt)));
  out_fields_.insert(std::make_pair("header_set_count", std::to_string(field_cnt)));

  DealCustomField();
  ParserBody(mp);
  ParserAttachment(mp);

  std::string result = dpi_contain_join(content_types_, ",");
  out_fields_.insert(std::make_pair("bodyType", result));
  out_fields_.insert(std::make_pair("bodyTypeCnt", std::to_string(content_types_.size())));
  result  = dpi_contain_join(content_transfer_encodings_, ",");
  out_fields_.insert({"bodyTraEnc", result});
  out_fields_.insert({"conTraEnc", result});

  result = dpi_contain_join(charsets_, ",");
  // out_fields_.insert(std::make_pair("bodyTexCha", result));
  for (auto &elem : fields_) {
    if (out_fields_.find(elem.first) != out_fields_.end()) {
      continue;
    }
    out_fields_.insert(elem);
  }
#ifdef LIBEMAIL_DEBUG
  std::cout << "---------------------------------------------------------------" << std::endl;
#endif

  for (auto &elem : out_fields_) {
#ifdef LIBEMAIL_DEBUG
    std::cout << elem.first << ": " << elem.second << std::endl;
#endif
    vmime::charset ch(elem.second);
  }
#ifdef LIBEMAIL_DEBUG
  std::cout << "---------------------------------------------------------------" << std::endl;
  std::cout << std::endl;
#endif
}

bool DpiEmail::RecordPut(precord_t *record) {
  ya_fvalue_t *fvalue;
  for (auto &field : out_fields_) {
    if (precord_field_get_by_name(record, field.first.c_str()) == nullptr) {
      continue;
    }
    fvalue = ya_fvalue_new_stringn(YA_FT_STRING, field.second.c_str(),
                                   field.second.length());

    precord_fvalue_put(record, field.first.c_str(), fvalue);
  }

  return true;
}

dpi_email_t * dpi_email_create()
{
    return new DpiEmail;
}

int dpi_email_imf(dpi_email_t *email, precord_t *record, const char *data, int len)
{
  //避免存在丢包邮件造成异常抛出
  try {
    email->dissectImf(record, data, len);
  }
  catch (const std::exception &e)
  {
     return -1;
  }
}

int dpi_email_imf_norecord(dpi_email_t *email, const char *data)
{

}

int dpi_email_imf_attachment(dpi_email_t *email, precord_t *record, const char *data, int len)
{
  email->SetFlag(0x01);
  //避免存在丢包邮件造成异常抛出
  try {
    email->dissectImf(record, data, len);
  }
  catch (const std::exception &e)
  {
     return -1;
  }

  return 0;
}

int dpi_email_record_put(dpi_email_t *email, precord_t *record)
{

  email->RecordPut(record);

  return 0;
}


char * dpi_email_get_by_name(dpi_email_t *email, const char * name)
{
  return email->GetByName(name);
}

void  dpi_email_destory(dpi_email_t * email)
{
    delete email;
}

void dpi_email_attach_foreach(dpi_email_t *email, void (*callback)(dpi_email_attach_t *, void *), void *arg)
{
  email->AttachForeach(callback, arg);
}

size_t dpi_email_attach_get_length(dpi_email_attach_t *attach)
{
  return attach->GetLength();
}

size_t dpi_email_attach_get_data(dpi_email_attach_t *attach, char *buf, size_t len)
{
  return attach->GetData(buf, len);
}

const char * dpi_email_attach_get_name(dpi_email_attach_t *attach)
{
  return attach->GetName();
}
