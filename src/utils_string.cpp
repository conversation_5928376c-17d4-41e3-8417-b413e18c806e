#include "utils.h"

#include <vector>
#include <string>

bool dpi_string_join(std::string &in, std::string &out, std::string joiner)
{
    if (out.empty()) {
        out = in;
        return true;
    }

    out = out + joiner + in;

    return true;
}


/*
* delete space in the head and tail of the string
*/
bool dpi_string_strip(std::string &str)
{
  size_t len = str.length();
  for (int i = 0; i < len; ++i) {
    // only delete space 
    if (str[i] == ' ') {
      str.erase(i, 1);
      --len;
      --i;
      continue;
    }
    break;
  }

  for (int i = str.length() - 1; i > 0; --i) {
    // only delete space 
    if (str[i] == ' ') {
      str.erase(i, 1);
      continue;
    }
    break;
  }

  return 0;
}


template <typename T>
std::string dpi_contain_join(T contain, std::string delimiter)
{
    std::string result;
    for (auto it = contain.begin(); it != contain.end(); ++it) {
        dpi_string_join(*it, result, delimiter);
    }

    return result;
}


template std::string dpi_contain_join<std::vector<std::string>>(std::vector<std::string> contain, std::string delimiter);
