#ifndef EMAIL_H
#define EMAIL_H

#include "vmime/message.hpp"
#include "vmime/messageParser.hpp"
#include "vmime/types.hpp"
#include <unordered_map>
#include <utility>
#include <vector>
#include <yaProtoRecord/precord.h>


struct DpiEmailAttachment
{
public:
  size_t GetData(char *buf, size_t len) {
    size_t copy_len = len > data_.size() ? data_.size() : len;
    memcpy(buf, data_.c_str(), copy_len);
    return copy_len;
  }

  size_t GetLength() {
    return data_.size();
  }

    const char * GetName() {
        return name_.c_str();
    }

public:
  std::string name_;
  std::string data_;
};

struct DpiEmail
{
public:
    void dissectImf(const char *);
    void dissectImf(precord_t * record, const char *, int);


    std::vector<std::string> GetFieldValueList(const std::string &key);

    bool DealCustomField();

    bool RecordPut(precord_t * record);

    inline bool PushCustomField(const std::string &key, const std::string &value) {
        out_fields_.insert(std::make_pair(key, value));
        return true;
    }

    bool AddCustomField(const std::string &new_key, const std::string &key);

    char * GetByName(const char * name){
        auto it = out_fields_.find(name);
        if (it != out_fields_.end()) {
            return const_cast<char *>(it->second.c_str());
        }
        return nullptr;
    }

    inline void SetFlag(uint8_t flag) {
        this->flag_ = flag | this->flag_;
    }

    std::vector<DpiEmailAttachment *> & GetAttachments() {
        return attachments_;
    }

    void AttachForeach(void (*callback)(DpiEmailAttachment *, void *), void *arg) {
        for (auto &attach : attachments_) {
            callback(attach, arg);
        }
    }

private:
    bool ParserHeader();
    bool ParserBody(vmime::shared_ptr<vmime::messageParser> &);
    bool ParserAttachment(vmime::shared_ptr<vmime::messageParser> &);
private:
    std::unordered_map<std::string, std::string> fields_;
    std::unordered_map<std::string, std::string> out_fields_;

    std::vector<std::string> content_types_;
    std::vector<std::string> content_transfer_encodings_;
    std::vector<std::string> charsets_;

    std::vector<DpiEmailAttachment *> attachments_;

    vmime::shared_ptr<vmime::message> msg_ = vmime::make_shared<vmime::message>();

    uint8_t flag_ = 0;    // dissect attachment   flag & 0x01 
};

#endif // YA_EMAIL_H