# project name email
cmake_minimum_required(VERSION 3.17)
find_package(yaBasicUtils REQUIRED)
project(yaemail VERSION 0.0.13 LANGUAGES C CXX)

SET(EMAIL_LIBRARY_NAME yaemail)

set(CMAKE_CXX_STANDARD 11)
set(CMAKE_C_STANDARD 11)
set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
set(CMAKE_BUILD_TYPE Debug)

# source files
FILE (
  GLOB_RECURSE
  EMAIL_SRC_FILES
  ${CMAKE_CURRENT_SOURCE_DIR}/src/*.cpp
)

FILE (
  GLOB_RECURSE
  EMAIL_HEADER_FILES
  ${CMAKE_CURRENT_SOURCE_DIR}/include/*.h
  ${CMAKE_CURRENT_SOURCE_DIR}/src/*.h
)

option(BUILD_SHARED_LIBS "Build shared libraries" OFF)


# add_library(${PROJECT_NAME}
#   src/email.cpp
# )

set(ENV{PKG_CONFIG_PATH} "$ENV{PKG_CONFIG_PATH}:/usr/local/lib64/pkgconfig")
find_package(PkgConfig REQUIRED)
pkg_check_modules(yaProtoRecord REQUIRED IMPORTED_TARGET libyaProtoRecord)
pkg_check_modules(yaFtypes REQUIRED IMPORTED_TARGET libyaFtypes)


OPTION(
	BUILD_STATIC_LIBRARY
	"Build static library"
	ON
)

if(BUILD_STATIC_LIBRARY)
 
  add_library(
    ${EMAIL_LIBRARY_NAME}
    STATIC
    ${EMAIL_SRC_FILES}
    ${EMAIL_HEADER_FILES}
  )

  target_include_directories( ${EMAIL_LIBRARY_NAME}
    PUBLIC
    ${CMAKE_CURRENT_SOURCE_DIR}/include
    ${CMAKE_CURRENT_SOURCE_DIR}/src
  )

  target_link_libraries(${EMAIL_LIBRARY_NAME}
    PRIVATE
    glib-2.0
    PkgConfig::yaFtypes
    PkgConfig::yaProtoRecord
    # libetpan
    # ${CMAKE_SOURCE_DIR}/lib/libetpan.a
    ${CMAKE_SOURCE_DIR}/lib/libvmime.a
    icuuc
    anl
  )

endif()

# open verbose
set(CMAKE_VERBOSE_MAKEFILE ON)


set_target_properties(${EMAIL_LIBRARY_NAME} PROPERTIES
  VERSION ${PROJECT_VERSION}
  SOVERSION 1
  LIBRARY_OUTPUT_DIRECTORY ${CMAKE_SOURCE_DIR}/lib
  PUBLIC_HEADER ${CMAKE_SOURCE_DIR}/include/yaemail/email.h
)

# target_include_directories(${PROJECT_NAME} 
#   PRIVATE 
#     ${CMAKE_SOURCE_DIR}/include
# )

# target_link_libraries(${PROJECT_NAME} 
#   PRIVATE 
#     ${CMAKE_SOURCE_DIR}/lib/libetpan.a
# )

############################################################
# tests 
# OPTION(BUILD_TESTS "Build tests" ON)
# if(BUILD_TESTS)
  add_subdirectory(tests)
  # endif()
  
  ############################################################
include(GNUInstallDirs)

set(INSTALL_LIBDIR ${CMAKE_INSTALL_LIBDIR} CACHE PATH "Installation directory for libraries")
set(INSTALL_BINDIR ${CMAKE_INSTALL_BINDIR} CACHE PATH "Installation directory for executables")
set(INSTALL_INCLUDEDIR ${CMAKE_INSTALL_INCLUDEDIR} CACHE PATH "Installation directory for header files")


# 报告安装位置
message(STATUS "Project will be installed to ${CMAKE_INSTALL_PREFIX}")
foreach(p LIB BIN INCLUDE)
  file(TO_NATIVE_PATH ${CMAKE_INSTALL_PREFIX}/${INSTALL_${p}DIR} _path)
  message(STATUS "Installing ${p} components to ${_path}")
  unset(_path)
endforeach()

message(STATUS "CMAKE_INSTALL_PREFIX: ${CMAKE_INSTALL_PREFIX}")
message(STATUS "CMAKE_INSTALL_LIBDIR: ${CMAKE_INSTALL_LIBDIR}")
message(STATUS "CMAKE_INSTALL_INCLUDEDIR: ${CMAKE_INSTALL_INCLUDEDIR}")

# install
install (TARGETS ${EMAIL_LIBRARY_NAME}
  # LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
  LIBRARY
    DESTINATION ${INSTALL_BINDIR}
    COMPONENT  lib
  PUBLIC_HEADER 
    DESTINATION ${CMAKE_INSTALL_INCLUDEDIR}/yaemail
)


  # install, must include GUNInstallDirs before any install command.

#
# install pkg-config file
#
# string(REPLACE ";" " " PROTO_RECORD_DEPENDS_LIB "${YA_FTYPES_STATIC_LDFLAGS} ${LUA_STATIC_LDFLAGS}")
configure_file(cmake/libyaemail.pc.in ${CMAKE_BINARY_DIR}/libyaemail.pc @ONLY)
install(FILES ${CMAKE_BINARY_DIR}/libyaemail.pc DESTINATION ${CMAKE_INSTALL_LIBDIR}/pkgconfig)

install(FILES ${CMAKE_SOURCE_DIR}/lib/libvmime.a DESTINATION ${INSTALL_LIBDIR})

ya_make_package(libyaemail "libicu libicu-devel")
